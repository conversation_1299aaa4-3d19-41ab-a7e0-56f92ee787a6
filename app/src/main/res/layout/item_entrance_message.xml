<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="8dp"
    android:background="@drawable/bg_entrance_message"
    android:layout_marginBottom="4dp"
    android:layout_marginStart="8dp"
    android:layout_marginEnd="8dp"
    android:gravity="center_vertical">

    <TextView
        android:id="@+id/tvEntranceContent"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:textColor="#FF9800"
        android:textSize="14sp"
        android:text="用户进入房间" />

    <TextView
        android:id="@+id/tvTime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#999999"
        android:textSize="12sp"
        android:text="12:34:56" />

</LinearLayout>
