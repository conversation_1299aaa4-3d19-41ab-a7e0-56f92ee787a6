package com.foisx.recycleview_animtion.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.foisx.recycleview_animtion.R
import com.foisx.recycleview_animtion.model.ChatMessage
import com.foisx.recycleview_animtion.model.MessageType
import java.text.SimpleDateFormat
import java.util.*

/**
 * 聊天消息适配器
 * 使用ListAdapter实现高效的数据更新和动画
 */
class ChatMessageAdapter : ListAdapter<ChatMessage, ChatMessageAdapter.MessageViewHolder>(MessageDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MessageViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_chat_message, parent, false)
        return MessageViewHolder(view)
    }

    override fun onBindViewHolder(holder: MessageViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    /**
     * 消息ViewHolder
     */
    class MessageViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val speechLayout: View = itemView.findViewById(R.id.speechLayout)
        private val entranceLayout: View = itemView.findViewById(R.id.entranceLayout)
        private val tvSpeechContent: TextView = itemView.findViewById(R.id.tvSpeechContent)
        private val tvSpeechTime: TextView = itemView.findViewById(R.id.tvSpeechTime)
        private val tvEntranceContent: TextView = itemView.findViewById(R.id.tvEntranceContent)

        private val timeFormat = SimpleDateFormat("HH:mm:ss", Locale.getDefault())

        fun bind(message: ChatMessage) {
            when (message.type) {
                MessageType.SPEECH -> {
                    speechLayout.visibility = View.VISIBLE
                    entranceLayout.visibility = View.GONE
                    tvSpeechContent.text = message.content
                    tvSpeechTime.text = timeFormat.format(Date(message.timestamp))
                }
                MessageType.ENTRANCE -> {
                    speechLayout.visibility = View.GONE
                    entranceLayout.visibility = View.VISIBLE
                    tvEntranceContent.text = "${message.username} 进入了房间"
                }
            }
        }
    }

    /**
     * DiffUtil回调，用于高效计算列表差异
     */
    private class MessageDiffCallback : DiffUtil.ItemCallback<ChatMessage>() {
        override fun areItemsTheSame(oldItem: ChatMessage, newItem: ChatMessage): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: ChatMessage, newItem: ChatMessage): Boolean {
            return oldItem == newItem
        }
    }
}
