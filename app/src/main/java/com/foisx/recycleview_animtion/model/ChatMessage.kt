package com.foisx.recycleview_animtion.model

import java.util.UUID

/**
 * 聊天消息数据模型
 * @param id 消息唯一标识
 * @param type 消息类型
 * @param content 消息内容
 * @param timestamp 消息时间戳
 * @param username 用户名（进场消息时使用）
 */
data class ChatMessage(
    val id: String = UUID.randomUUID().toString(),
    val type: MessageType,
    val content: String,
    val timestamp: Long = System.currentTimeMillis(),
    val username: String = ""
)

/**
 * 消息类型枚举
 */
enum class MessageType {
    SPEECH,    // 发言消息
    ENTRANCE   // 进场消息
}
