// SmartMessageDispatcher.kt
package com.foisx.recycleview_animtion.manager

import android.util.Log
import com.foisx.recycleview_animtion.adapter.ChatAdapter
import com.foisx.recycleview_animtion.model.ChatMessage
import kotlinx.coroutines.*
import java.util.concurrent.ConcurrentLinkedQueue

class SmartMessageDispatcher(
    private val layoutManager: ChatLayoutManager,
    private val adapter: ChatAdapter
) {
    private val messageBuffer = ConcurrentLinkedQueue<ChatMessage>()
    private var processingJob: Job? = null
    private val coroutineScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    
    fun dispatchMessage(message: ChatMessage) {
        messageBuffer.offer(message)
        
        // 取消之前的处理任务，重新批量处理
        processingJob?.cancel()
        processingJob = coroutineScope.launch {
            delay(16) // 一帧的时间，批量处理消息
            processBatchMessages()
        }
    }
    
    private suspend fun processBatchMessages() {
        val batch = mutableListOf<ChatMessage>()
        
        // 批量取出消息（最多5条）
        while (messageBuffer.isNotEmpty() && batch.size < 5) {
            messageBuffer.poll()?.let { batch.add(it) }
        }
        
        Log.d("SmartMessageDispatcher", "批量处理 ${batch.size} 条消息")
        
        // 根据当前状态分发消息
        batch.forEach { message ->
            when (layoutManager.getCurrentState()) {
                ChatLayoutManager.LayoutState.UNIFIED -> {
                    layoutManager.handleUnifiedMode(message)
                }
                ChatLayoutManager.LayoutState.SEPARATED -> {
                    layoutManager.handleSeparatedMode(message)
                }
                ChatLayoutManager.LayoutState.TRANSITIONING -> {
                    // 过渡期间的消息重新入队
                    queueForLater(message)
                }
            }
            
            // 每条消息之间稍微延迟，避免UI卡顿
            delay(50)
        }
    }
    
    private fun queueForLater(message: ChatMessage) {
        coroutineScope.launch {
            delay(200) // 等待过渡完成
            dispatchMessage(message)
        }
    }
    
    fun getQueueSize(): Int = messageBuffer.size
    
    fun clearQueue() {
        messageBuffer.clear()
        processingJob?.cancel()
    }
}
