// ChatLayoutManager.kt
package com.foisx.recycleview_animtion.manager

import android.util.Log
import androidx.recyclerview.widget.RecyclerView
import com.foisx.recycleview_animtion.adapter.ChatAdapter
import com.foisx.recycleview_animtion.model.ChatMessage
import com.foisx.recycleview_animtion.model.MessageType
import kotlinx.coroutines.*

class ChatLayoutManager(
    private val recyclerView: RecyclerView,
    private val adapter: ChatAdapter,
    private val onQueueSizeChanged: (() -> Unit)? = null
) {
    private var layoutState = LayoutState.UNIFIED
    private val messageDispatcher = SmartMessageDispatcher(this, adapter)
    private val transitionManager = RobustTransitionManager(recyclerView, adapter, onQueueSizeChanged)
    private val testManager = TestableLayoutManager()
    
    enum class LayoutState {
        UNIFIED,        // 前5条统一管理
        SEPARATED,      // 分层管理
        TRANSITIONING   // 过渡中
    }
    
    fun addMessage(message: ChatMessage) {
        Log.d("ChatLayoutManager", "添加消息: ${message.type}, 当前状态: $layoutState")
        messageDispatcher.dispatchMessage(message)
    }
    
    fun handleUnifiedMode(message: ChatMessage) {
        adapter.addMessage(message)
        scrollToTop()
        
        if (adapter.getMessageCount() > 5) {
            triggerTransitionToSeparated()
        }
    }
    
    fun handleSeparatedMode(message: ChatMessage) {
        when (message.type) {
            MessageType.ENTRANCE -> handleEntranceInSeparated(message)
            MessageType.SPEECH -> handleSpeechInSeparated(message)
        }
    }
    
    private fun handleEntranceInSeparated(message: ChatMessage) {
        val firstMessage = adapter.getFirstMessage()
        if (firstMessage?.type == MessageType.ENTRANCE) {
            transitionManager.executeEntranceReplacement(message)
        } else {
            adapter.addMessage(message)
            scrollToTop()
        }
    }
    
    private fun handleSpeechInSeparated(message: ChatMessage) {
        if (adapter.getMessageCount() > 0) {
            adapter.insertMessageAt(1, message)
        } else {
            adapter.addMessage(message)
        }
        scrollToTop()
    }
    
    private fun triggerTransitionToSeparated() {
        if (layoutState != LayoutState.TRANSITIONING) {
            layoutState = LayoutState.TRANSITIONING
            transitionManager.executeLayoutTransition {
                layoutState = LayoutState.SEPARATED
                Log.d("ChatLayoutManager", "成功切换到分离模式")
            }
        }
    }
    
    private fun scrollToTop() {
        recyclerView.post {
            recyclerView.smoothScrollToPosition(0)
        }
    }
    
    // 公开接口供其他组件使用
    fun getCurrentState(): LayoutState = layoutState
    fun isInTransition(): Boolean = layoutState == LayoutState.TRANSITIONING
    
    // 测试接口
    fun getTestManager(): TestableLayoutManager = testManager
    fun getTransitionManager(): RobustTransitionManager = transitionManager
    fun getMessageDispatcher(): SmartMessageDispatcher = messageDispatcher
}
