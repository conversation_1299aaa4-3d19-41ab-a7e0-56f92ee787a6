// RobustTransitionManager.kt
package com.foisx.recycleview_animtion.manager

import android.os.CountDownTimer
import android.util.Log
import androidx.recyclerview.widget.RecyclerView
import com.foisx.recycleview_animtion.adapter.ChatAdapter
import com.foisx.recycleview_animtion.model.ChatMessage
import java.util.concurrent.ConcurrentLinkedQueue

class RobustTransitionManager(
    private val recyclerView: RecyclerView,
    private val adapter: ChatAdapter
) {
    private val snapshotManager = ViewSnapshotManager()
    private val transitionAnimator = OptimizedTransitionAnimator()
    private val entranceQueue = ConcurrentLinkedQueue<ChatMessage>()

    private var fallbackTimer: CountDownTimer? = null
    private var isTransitioning = false

    fun executeEntranceReplacement(newMessage: ChatMessage) {
        if (isTransitioning) {
            // 如果正在过渡中，加入队列
            entranceQueue.offer(newMessage)
            return
        }

        try {
            isTransitioning = true
            startFallbackTimer()

            Log.d("RobustTransitionManager", "开始执行进场消息替换")

            val animator = transitionAnimator.createEntranceReplacementAnimation(
                recyclerView = recyclerView,
                newMessage = newMessage
            ) {
                // 动画完成回调：更新数据
                adapter.updateFirstMessage(newMessage)
            }

            animator?.addListener(object : android.animation.AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: android.animation.Animator) {
                    completeTransition()
                }

                override fun onAnimationCancel(animation: android.animation.Animator) {
                    completeTransition()
                }
            })

            animator?.start() ?: run {
                // 如果无法创建动画，直接更新数据
                adapter.updateFirstMessage(newMessage)
                completeTransition()
            }

        } catch (e: Exception) {
            Log.e("RobustTransitionManager", "过渡动画执行失败", e)
            fallbackToSimpleMode(newMessage)
        }
    }

    fun executeLayoutTransition(onComplete: () -> Unit) {
        try {
            isTransitioning = true
            startFallbackTimer()

            Log.d("RobustTransitionManager", "开始执行布局过渡")

            // 捕获当前状态快照
            val snapshot = snapshotManager.captureFirstItemSnapshot(recyclerView)

            // 执行布局切换动画（这里可以扩展更复杂的过渡效果）
            recyclerView.postDelayed({
                onComplete()
                completeTransition()
            }, 100)

        } catch (e: Exception) {
            Log.e("RobustTransitionManager", "布局过渡失败", e)
            onComplete()
            completeTransition()
        }
    }

    private fun startFallbackTimer() {
        fallbackTimer?.cancel()
        fallbackTimer = object : CountDownTimer(2000, 2000) {
            override fun onTick(millisUntilFinished: Long) {}
            override fun onFinish() {
                Log.w("RobustTransitionManager", "过渡超时，强制完成")
                forceCompleteTransition()
            }
        }.start()
    }

    private fun completeTransition() {
        isTransitioning = false
        fallbackTimer?.cancel()

        // 处理队列中的消息
        processQueuedMessages()
    }

    private fun forceCompleteTransition() {
        // 清理所有动画
        recyclerView.clearAnimation()

        // 强制完成状态
        completeTransition()
    }

    private fun fallbackToSimpleMode(message: ChatMessage) {
        Log.w("RobustTransitionManager", "回退到简单模式")
        adapter.updateFirstMessage(message)
        completeTransition()
    }

    private fun processQueuedMessages() {
        if (entranceQueue.isNotEmpty()) {
            val nextMessage = entranceQueue.poll()
            if (nextMessage != null) {
                // 延迟处理下一个消息
                recyclerView.postDelayed({
                    executeEntranceReplacement(nextMessage)
                }, 500)
            }
        }
    }

    fun getQueueSize(): Int = entranceQueue.size
    fun isInTransition(): Boolean = isTransitioning

    fun clearQueue() {
        entranceQueue.clear()
    }
}
